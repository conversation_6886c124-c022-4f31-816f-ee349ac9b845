import { NextRequest, NextResponse } from 'next/server'
import { getToolsFromNotion, addToolToNotion } from '@/lib/notion'

export async function GET() {
  try {
    const tools = await getToolsFromNotion()
    return NextResponse.json(tools)
  } catch (error) {
    console.error('Error fetching tools:', error)
    return NextResponse.json(
      { error: 'Failed to fetch tools' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate required fields
    if (!body.name || !body.description || !body.category || !body.url) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    const newTool = await addToolToNotion({
      name: body.name,
      description: body.description,
      category: body.category,
      url: body.url,
      tags: body.tags || [],
      featured: body.featured || false,
      rating: body.rating,
      usageCount: 0,
    })

    if (!newTool) {
      return NextResponse.json(
        { error: 'Failed to add tool' },
        { status: 500 }
      )
    }

    return NextResponse.json(newTool, { status: 201 })
  } catch (error) {
    console.error('Error adding tool:', error)
    return NextResponse.json(
      { error: 'Failed to add tool' },
      { status: 500 }
    )
  }
}
