import { Client } from '@notionhq/client'
import { Tool, Category } from '@/types/tool'

// Initialize Notion client
const notion = new Client({
  auth: process.env.NOTION_TOKEN,
})

// Your Notion database ID - replace with your actual database ID
const DATABASE_ID = process.env.NOTION_DATABASE_ID || ''

export async function getToolsFromNotion(): Promise<Tool[]> {
  try {
    if (!DATABASE_ID || !process.env.NOTION_TOKEN) {
      console.warn('Notion credentials not configured, using mock data')
      return []
    }

    const response = await notion.databases.query({
      database_id: DATABASE_ID,
      sorts: [
        {
          property: 'Featured',
          direction: 'descending',
        },
        {
          property: 'Name',
          direction: 'ascending',
        },
      ],
    })

    const tools: Tool[] = response.results.map((page: any) => {
      const properties = page.properties

      return {
        id: page.id,
        name: properties.Name?.title?.[0]?.plain_text || 'Untitled',
        description: properties.Description?.rich_text?.[0]?.plain_text || '',
        category: properties.Category?.select?.name as Category || 'Software Tools',
        url: properties.URL?.url || '#',
        tags: properties.Tags?.multi_select?.map((tag: any) => tag.name) || [],
        featured: properties.Featured?.checkbox || false,
        imageUrl: properties.Image?.files?.[0]?.file?.url || properties.Image?.files?.[0]?.external?.url,
        rating: properties.Rating?.number || undefined,
        usageCount: properties.UsageCount?.number || 0,
        dateAdded: properties.DateAdded?.date?.start || new Date().toISOString(),
        lastUpdated: properties.LastUpdated?.last_edited_time || new Date().toISOString(),
      }
    })

    return tools
  } catch (error) {
    console.error('Error fetching tools from Notion:', error)
    return []
  }
}

export async function addToolToNotion(tool: Omit<Tool, 'id'>): Promise<Tool | null> {
  try {
    if (!DATABASE_ID || !process.env.NOTION_TOKEN) {
      throw new Error('Notion credentials not configured')
    }

    const response = await notion.pages.create({
      parent: {
        database_id: DATABASE_ID,
      },
      properties: {
        Name: {
          title: [
            {
              text: {
                content: tool.name,
              },
            },
          ],
        },
        Description: {
          rich_text: [
            {
              text: {
                content: tool.description,
              },
            },
          ],
        },
        Category: {
          select: {
            name: tool.category,
          },
        },
        URL: {
          url: tool.url,
        },
        Tags: {
          multi_select: tool.tags.map(tag => ({ name: tag })),
        },
        Featured: {
          checkbox: tool.featured,
        },
        Rating: tool.rating ? {
          number: tool.rating,
        } : undefined,
        UsageCount: {
          number: tool.usageCount || 0,
        },
        DateAdded: {
          date: {
            start: new Date().toISOString(),
          },
        },
      },
    })

    return {
      ...tool,
      id: response.id,
      dateAdded: new Date().toISOString(),
      lastUpdated: new Date().toISOString(),
    }
  } catch (error) {
    console.error('Error adding tool to Notion:', error)
    return null
  }
}

export async function updateToolInNotion(toolId: string, updates: Partial<Tool>): Promise<boolean> {
  try {
    if (!process.env.NOTION_TOKEN) {
      throw new Error('Notion credentials not configured')
    }

    const properties: any = {}

    if (updates.name) {
      properties.Name = {
        title: [{ text: { content: updates.name } }],
      }
    }

    if (updates.description) {
      properties.Description = {
        rich_text: [{ text: { content: updates.description } }],
      }
    }

    if (updates.category) {
      properties.Category = {
        select: { name: updates.category },
      }
    }

    if (updates.url) {
      properties.URL = {
        url: updates.url,
      }
    }

    if (updates.tags) {
      properties.Tags = {
        multi_select: updates.tags.map(tag => ({ name: tag })),
      }
    }

    if (updates.featured !== undefined) {
      properties.Featured = {
        checkbox: updates.featured,
      }
    }

    if (updates.rating) {
      properties.Rating = {
        number: updates.rating,
      }
    }

    properties.LastUpdated = {
      date: {
        start: new Date().toISOString(),
      },
    }

    await notion.pages.update({
      page_id: toolId,
      properties,
    })

    return true
  } catch (error) {
    console.error('Error updating tool in Notion:', error)
    return false
  }
}

export async function deleteToolFromNotion(toolId: string): Promise<boolean> {
  try {
    if (!process.env.NOTION_TOKEN) {
      throw new Error('Notion credentials not configured')
    }

    await notion.pages.update({
      page_id: toolId,
      archived: true,
    })

    return true
  } catch (error) {
    console.error('Error deleting tool from Notion:', error)
    return false
  }
}
