'use client'

import { Category } from '@/types/tool'

interface CategoryFilterProps {
  categories: Category[]
  selectedCategory: Category | 'all'
  onCategoryChange: (category: Category | 'all') => void
}

export default function CategoryFilter({ 
  categories, 
  selectedCategory, 
  onCategoryChange 
}: CategoryFilterProps) {
  const allCategories = ['all', ...categories] as const

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'AI Vibe Coding Tools': return '💻'
      case 'UI Designers': return '🎨'
      case 'AI Agents': return '🤖'
      case 'Research Tools': return '🔬'
      case 'Educational Tools': return '📚'
      case 'Academic Helping Tools': return '🎓'
      case 'Software Tools': return '⚙️'
      default: return '📁'
    }
  }

  const getCategoryLabel = (category: string) => {
    return category === 'all' ? 'All Tools' : category
  }

  return (
    <div className="mb-8">
      <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-4">Categories</h2>
      
      {/* Desktop horizontal scroll */}
      <div className="hidden md:flex space-x-3 overflow-x-auto pb-2">
        {allCategories.map((category) => (
          <button
            key={category}
            onClick={() => onCategoryChange(category)}
            className={`flex items-center space-x-2 px-4 py-2 rounded-full whitespace-nowrap transition-all ${
              selectedCategory === category
                ? 'bg-blue-600 text-white shadow-lg transform scale-105'
                : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 border border-gray-200 dark:border-gray-600'
            }`}
          >
            <span className="text-lg">{getCategoryIcon(category)}</span>
            <span className="font-medium">{getCategoryLabel(category)}</span>
          </button>
        ))}
      </div>

      {/* Mobile dropdown */}
      <div className="md:hidden">
        <select
          value={selectedCategory}
          onChange={(e) => onCategoryChange(e.target.value as Category | 'all')}
          className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          {allCategories.map((category) => (
            <option key={category} value={category}>
              {getCategoryIcon(category)} {getCategoryLabel(category)}
            </option>
          ))}
        </select>
      </div>
    </div>
  )
}
