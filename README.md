# Directory Tools App

A comprehensive directory application for organizing your tools, AI agents, and resources with Notion CRM integration.

## Features

- 📁 **Categorized Tool Directory**: Organize tools by categories like AI Vibe Coding Tools, UI Designers, AI Agents, Research Tools, Educational Tools, Academic Helping Tools, and Software Tools
- 🔍 **Advanced Search**: Search by tool name, description, or tags
- 🏷️ **Category Filtering**: Filter tools by specific categories
- 🌟 **Featured Tools**: Highlight your most important tools
- 📱 **Responsive Design**: Works perfectly on desktop and mobile devices
- 🔗 **Direct Links**: Clickable buttons that redirect to associated websites
- 🗃️ **Notion CRM Integration**: Backend powered by Notion database

## Tech Stack

- **Frontend**: Next.js 15, React 18, TypeScript
- **Styling**: Tailwind CSS
- **Backend**: Notion API
- **Icons**: Lucide React
- **Deployment**: Ready for Vercel

## Getting Started

### Prerequisites

- Node.js 18+ installed
- A Notion account and workspace

### Installation

1. Clone the repository:
```bash
git clone <your-repo-url>
cd directory-tools-app
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.local.example .env.local
```

4. Configure your Notion integration (see Notion Setup section below)

5. Run the development server:
```bash
npm run dev
```

6. Open [http://localhost:3000](http://localhost:3000) in your browser

## Notion Setup

### 1. Create a Notion Integration

1. Go to [Notion Developers](https://developers.notion.com/)
2. Click "New integration"
3. Fill in the integration details
4. Copy the "Internal Integration Token"

### 2. Create a Notion Database

Create a new database in Notion with the following properties:

| Property Name | Property Type | Description |
|---------------|---------------|-------------|
| Name | Title | Tool name |
| Description | Text | Tool description |
| Category | Select | Tool category (create options for each category) |
| URL | URL | Link to the tool |
| Tags | Multi-select | Tool tags |
| Featured | Checkbox | Whether the tool is featured |
| Image | Files & media | Tool logo/image (optional) |
| Rating | Number | Tool rating (optional) |
| UsageCount | Number | Usage tracking (optional) |
| DateAdded | Date | When the tool was added |
| LastUpdated | Last edited time | Auto-updated |

### 3. Share Database with Integration

1. Open your database in Notion
2. Click "Share" in the top right
3. Click "Invite" and select your integration
4. Copy the database ID from the URL

### 4. Update Environment Variables

Update your `.env.local` file:

```env
NOTION_TOKEN=your_integration_token_here
NOTION_DATABASE_ID=your_database_id_here
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

## Project Structure

```
src/
├── app/
│   ├── globals.css
│   ├── layout.tsx
│   └── page.tsx
├── components/
│   ├── CategoryFilter.tsx
│   ├── Header.tsx
│   ├── SearchBar.tsx
│   └── ToolCard.tsx
├── lib/
│   └── notion.ts
└── types/
    └── tool.ts
```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## Customization

### Adding New Categories

1. Update the `Category` type in `src/types/tool.ts`
2. Add the new category to the categories array in `src/app/page.tsx`
3. Add appropriate styling in `src/components/CategoryFilter.tsx` and `src/components/ToolCard.tsx`

### Styling

The app uses Tailwind CSS for styling. You can customize:
- Colors in `tailwind.config.js`
- Global styles in `src/app/globals.css`
- Component-specific styles in individual component files

## Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add your environment variables in Vercel dashboard
4. Deploy

### Other Platforms

The app can be deployed to any platform that supports Next.js applications.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

If you encounter any issues or have questions, please create an issue in the repository.
