'use client'

import { useState, useEffect } from 'react'
import Header from '@/components/Header'
import CategoryFilter from '@/components/CategoryFilter'
import ToolCard from '@/components/ToolCard'
import SearchBar from '@/components/SearchBar'
import AddToolModal from '@/components/AddToolModal'
import { Tool, Category } from '@/types/tool'
import { getToolsFromNotion } from '@/lib/notion'

export default function Home() {
  const [tools, setTools] = useState<Tool[]>([])
  const [filteredTools, setFilteredTools] = useState<Tool[]>([])
  const [selectedCategory, setSelectedCategory] = useState<Category | 'all'>('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [loading, setLoading] = useState(true)
  const [isAddModalOpen, setIsAddModalOpen] = useState(false)

  // Mock data for development - replace with Notion API call
  const mockTools: Tool[] = [
    {
      id: '1',
      name: 'ChatGPT',
      description: 'Advanced AI chatbot for conversations and assistance',
      category: 'AI Agents',
      url: 'https://chat.openai.com',
      tags: ['AI', 'Chat', 'Assistant'],
      featured: true
    },
    {
      id: '2',
      name: 'Figma',
      description: 'Collaborative interface design tool',
      category: 'UI Designers',
      url: 'https://figma.com',
      tags: ['Design', 'UI', 'Collaboration'],
      featured: true
    },
    {
      id: '3',
      name: 'GitHub Copilot',
      description: 'AI-powered code completion tool',
      category: 'AI Vibe Coding Tools',
      url: 'https://github.com/features/copilot',
      tags: ['AI', 'Coding', 'Productivity'],
      featured: false
    },
    {
      id: '4',
      name: 'Notion',
      description: 'All-in-one workspace for notes and collaboration',
      category: 'Educational Tools',
      url: 'https://notion.so',
      tags: ['Notes', 'Organization', 'Collaboration'],
      featured: true
    },
    {
      id: '5',
      name: 'ResearchGate',
      description: 'Social networking site for scientists and researchers',
      category: 'Research Tools',
      url: 'https://researchgate.net',
      tags: ['Research', 'Academic', 'Networking'],
      featured: false
    }
  ]

  useEffect(() => {
    // Simulate API call
    const loadTools = async () => {
      setLoading(true)
      try {
        // In production, replace with: const data = await getToolsFromNotion()
        await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate loading
        setTools(mockTools)
        setFilteredTools(mockTools)
      } catch (error) {
        console.error('Error loading tools:', error)
        setTools(mockTools) // Fallback to mock data
        setFilteredTools(mockTools)
      } finally {
        setLoading(false)
      }
    }

    loadTools()
  }, [])

  useEffect(() => {
    let filtered = tools

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(tool => tool.category === selectedCategory)
    }

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(tool =>
        tool.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        tool.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        tool.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      )
    }

    setFilteredTools(filtered)
  }, [tools, selectedCategory, searchQuery])

  const categories: Category[] = [
    'AI Vibe Coding Tools',
    'UI Designers',
    'AI Agents',
    'Research Tools',
    'Educational Tools',
    'Academic Helping Tools',
    'Software Tools'
  ]

  const handleAddTool = (newTool: Tool) => {
    setTools(prev => [newTool, ...prev])
  }

  return (
    <main className="container mx-auto px-4 py-8">
      <Header onAddTool={() => setIsAddModalOpen(true)} />
      
      <div className="mb-8">
        <SearchBar 
          searchQuery={searchQuery}
          onSearchChange={setSearchQuery}
        />
      </div>

      <div className="mb-8">
        <CategoryFilter
          categories={categories}
          selectedCategory={selectedCategory}
          onCategoryChange={setSelectedCategory}
        />
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredTools.map((tool) => (
            <ToolCard key={tool.id} tool={tool} />
          ))}
        </div>
      )}

      {!loading && filteredTools.length === 0 && (
        <div className="text-center py-16">
          <p className="text-gray-500 text-lg">No tools found matching your criteria.</p>
        </div>
      )}

      <AddToolModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onAdd={handleAddTool}
      />
    </main>
  )
}
