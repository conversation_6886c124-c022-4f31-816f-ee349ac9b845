[{"name": "generate-buildid", "duration": 439, "timestamp": 61513880014, "id": 4, "parentId": 1, "tags": {}, "startTime": 1749455974743, "traceId": "b908085b5d9a9c26"}, {"name": "load-custom-routes", "duration": 795, "timestamp": 61513880674, "id": 5, "parentId": 1, "tags": {}, "startTime": 1749455974743, "traceId": "b908085b5d9a9c26"}, {"name": "create-dist-dir", "duration": 1125, "timestamp": 61514063278, "id": 6, "parentId": 1, "tags": {}, "startTime": 1749455974926, "traceId": "b908085b5d9a9c26"}, {"name": "create-pages-mapping", "duration": 521, "timestamp": 61514177049, "id": 7, "parentId": 1, "tags": {}, "startTime": 1749455975040, "traceId": "b908085b5d9a9c26"}, {"name": "collect-app-paths", "duration": 14398, "timestamp": 61514177683, "id": 8, "parentId": 1, "tags": {}, "startTime": 1749455975040, "traceId": "b908085b5d9a9c26"}, {"name": "create-app-mapping", "duration": 4607, "timestamp": 61514192151, "id": 9, "parentId": 1, "tags": {}, "startTime": 1749455975055, "traceId": "b908085b5d9a9c26"}, {"name": "public-dir-conflict-check", "duration": 21637, "timestamp": 61514198540, "id": 10, "parentId": 1, "tags": {}, "startTime": 1749455975061, "traceId": "b908085b5d9a9c26"}, {"name": "generate-routes-manifest", "duration": 5266, "timestamp": 61514220839, "id": 11, "parentId": 1, "tags": {}, "startTime": 1749455975083, "traceId": "b908085b5d9a9c26"}, {"name": "next-build", "duration": 9234839, "timestamp": 61513597121, "id": 1, "tags": {"buildMode": "default", "isTurboBuild": "false", "version": "15.3.3", "has-custom-webpack-config": "false", "use-build-worker": "true"}, "startTime": 1749455974460, "traceId": "b908085b5d9a9c26"}]