'use client'

import { ExternalLink, Star, Tag } from 'lucide-react'
import { Tool } from '@/types/tool'

interface ToolCardProps {
  tool: Tool
}

export default function ToolCard({ tool }: ToolCardProps) {
  const handleClick = () => {
    window.open(tool.url, '_blank', 'noopener,noreferrer')
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'AI Vibe Coding Tools': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'UI Designers': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
      case 'AI Agents': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'Research Tools': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'
      case 'Educational Tools': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      case 'Academic Helping Tools': return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200'
      case 'Software Tools': return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
    }
  }

  return (
    <div className="group relative bg-white dark:bg-gray-800 rounded-xl shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-gray-200 dark:border-gray-700 overflow-hidden">
      {tool.featured && (
        <div className="absolute top-3 right-3 z-10">
          <div className="bg-yellow-400 text-yellow-900 px-2 py-1 rounded-full text-xs font-semibold flex items-center space-x-1">
            <Star size={12} fill="currentColor" />
            <span>Featured</span>
          </div>
        </div>
      )}

      <div className="p-6">
        <div className="mb-4">
          <div className={`inline-block px-3 py-1 rounded-full text-xs font-semibold mb-3 ${getCategoryColor(tool.category)}`}>
            {tool.category}
          </div>
          
          <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
            {tool.name}
          </h3>
          
          <p className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed mb-4">
            {tool.description}
          </p>
        </div>

        {tool.tags && tool.tags.length > 0 && (
          <div className="mb-4">
            <div className="flex flex-wrap gap-2">
              {tool.tags.slice(0, 3).map((tag, index) => (
                <span
                  key={index}
                  className="inline-flex items-center space-x-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-2 py-1 rounded-md text-xs"
                >
                  <Tag size={10} />
                  <span>{tag}</span>
                </span>
              ))}
              {tool.tags.length > 3 && (
                <span className="text-xs text-gray-500 dark:text-gray-400 px-2 py-1">
                  +{tool.tags.length - 3} more
                </span>
              )}
            </div>
          </div>
        )}

        <button
          onClick={handleClick}
          className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors flex items-center justify-center space-x-2 group"
        >
          <span>Visit Tool</span>
          <ExternalLink size={16} className="group-hover:translate-x-1 transition-transform" />
        </button>
      </div>

      {/* Hover effect overlay */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 to-purple-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
    </div>
  )
}
