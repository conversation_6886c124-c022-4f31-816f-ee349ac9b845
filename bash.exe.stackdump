Stack trace:
Frame         Function      Args
0007FFFFA100  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFFA100, 0007FFFF9000) msys-2.0.dll+0x1FEBA
0007FFFFA100  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA3D8) msys-2.0.dll+0x67F9
0007FFFFA100  000210046832 (000210285FF9, 0007FFFF9FB8, 0007FFFFA100, 000000000000) msys-2.0.dll+0x6832
0007FFFFA100  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFA100  0002100690B4 (0007FFFFA110, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFA3E0  00021006A49D (0007FFFFA110, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF9165F0000 ntdll.dll
7FF914F60000 KERNEL32.DLL
7FF913F80000 KERNELBASE.dll
7FF90FA60000 apphelp.dll
7FF915750000 USER32.dll
7FF913EA0000 win32u.dll
7FF916580000 GDI32.dll
7FF913C90000 gdi32full.dll
7FF913E00000 msvcp_win.dll
7FF914490000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF915030000 advapi32.dll
7FF915A10000 msvcrt.dll
7FF915AB0000 sechost.dll
7FF914620000 RPCRT4.dll
7FF914280000 bcrypt.dll
7FF913550000 CRYPTBASE.DLL
7FF914590000 bcryptPrimitives.dll
7FF916550000 IMM32.DLL
