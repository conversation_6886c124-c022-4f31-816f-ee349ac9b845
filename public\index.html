<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Directory Tools App - Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .tool-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .tool-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <header class="mb-12">
            <div class="flex items-center justify-between mb-6">
                <div>
                    <h1 class="text-4xl font-bold text-gray-800 mb-2">Directory Tools Hub</h1>
                    <p class="text-gray-600 text-lg">Your comprehensive collection of AI tools, resources, and utilities</p>
                </div>
                <button class="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-4v4h-2v-4H7v-2h4V7h2v4h4v2z"/>
                    </svg>
                    <span>Add Tool</span>
                </button>
            </div>
            <div class="bg-gradient-to-r from-blue-600 to-purple-600 h-1 rounded-full"></div>
        </header>

        <!-- Search Bar -->
        <div class="mb-8">
            <div class="relative max-w-2xl mx-auto">
                <div class="relative">
                    <svg class="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                    </svg>
                    <input type="text" placeholder="Search tools, categories, or tags..." class="w-full pl-12 pr-4 py-4 text-lg border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white text-gray-900 placeholder-gray-500 shadow-sm">
                </div>
            </div>
        </div>

        <!-- Categories -->
        <div class="mb-8">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Categories</h2>
            <div class="flex space-x-3 overflow-x-auto pb-2">
                <button class="flex items-center space-x-2 px-4 py-2 rounded-full whitespace-nowrap bg-blue-600 text-white shadow-lg transform scale-105">
                    <span class="text-lg">📁</span>
                    <span class="font-medium">All Tools</span>
                </button>
                <button class="flex items-center space-x-2 px-4 py-2 rounded-full whitespace-nowrap bg-white text-gray-700 hover:bg-gray-100 border border-gray-200">
                    <span class="text-lg">💻</span>
                    <span class="font-medium">AI Vibe Coding Tools</span>
                </button>
                <button class="flex items-center space-x-2 px-4 py-2 rounded-full whitespace-nowrap bg-white text-gray-700 hover:bg-gray-100 border border-gray-200">
                    <span class="text-lg">🎨</span>
                    <span class="font-medium">UI Designers</span>
                </button>
                <button class="flex items-center space-x-2 px-4 py-2 rounded-full whitespace-nowrap bg-white text-gray-700 hover:bg-gray-100 border border-gray-200">
                    <span class="text-lg">🤖</span>
                    <span class="font-medium">AI Agents</span>
                </button>
                <button class="flex items-center space-x-2 px-4 py-2 rounded-full whitespace-nowrap bg-white text-gray-700 hover:bg-gray-100 border border-gray-200">
                    <span class="text-lg">🔬</span>
                    <span class="font-medium">Research Tools</span>
                </button>
            </div>
        </div>

        <!-- Tools Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            <!-- Tool Card 1 -->
            <div class="tool-card group relative bg-white rounded-xl shadow-md border border-gray-200 overflow-hidden">
                <div class="absolute top-3 right-3 z-10">
                    <div class="bg-yellow-400 text-yellow-900 px-2 py-1 rounded-full text-xs font-semibold flex items-center space-x-1">
                        <svg width="12" height="12" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                        <span>Featured</span>
                    </div>
                </div>
                <div class="p-6">
                    <div class="mb-4">
                        <div class="inline-block px-3 py-1 rounded-full text-xs font-semibold mb-3 bg-blue-100 text-blue-800">
                            AI Agents
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                            ChatGPT
                        </h3>
                        <p class="text-gray-600 text-sm leading-relaxed mb-4">
                            Advanced AI chatbot for conversations and assistance
                        </p>
                    </div>
                    <div class="mb-4">
                        <div class="flex flex-wrap gap-2">
                            <span class="inline-flex items-center space-x-1 bg-gray-100 text-gray-700 px-2 py-1 rounded-md text-xs">
                                <span>AI</span>
                            </span>
                            <span class="inline-flex items-center space-x-1 bg-gray-100 text-gray-700 px-2 py-1 rounded-md text-xs">
                                <span>Chat</span>
                            </span>
                            <span class="inline-flex items-center space-x-1 bg-gray-100 text-gray-700 px-2 py-1 rounded-md text-xs">
                                <span>Assistant</span>
                            </span>
                        </div>
                    </div>
                    <button onclick="window.open('https://chat.openai.com', '_blank')" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors flex items-center justify-center space-x-2 group">
                        <span>Visit Tool</span>
                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24" class="group-hover:translate-x-1 transition-transform">
                            <path d="M19 19H5V5h7V3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2v-7h-2v7zM14 3v2h3.59l-9.83 9.83 1.41 1.41L19 6.41V10h2V3h-7z"/>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Tool Card 2 -->
            <div class="tool-card group relative bg-white rounded-xl shadow-md border border-gray-200 overflow-hidden">
                <div class="absolute top-3 right-3 z-10">
                    <div class="bg-yellow-400 text-yellow-900 px-2 py-1 rounded-full text-xs font-semibold flex items-center space-x-1">
                        <svg width="12" height="12" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                        <span>Featured</span>
                    </div>
                </div>
                <div class="p-6">
                    <div class="mb-4">
                        <div class="inline-block px-3 py-1 rounded-full text-xs font-semibold mb-3 bg-purple-100 text-purple-800">
                            UI Designers
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                            Figma
                        </h3>
                        <p class="text-gray-600 text-sm leading-relaxed mb-4">
                            Collaborative interface design tool
                        </p>
                    </div>
                    <div class="mb-4">
                        <div class="flex flex-wrap gap-2">
                            <span class="inline-flex items-center space-x-1 bg-gray-100 text-gray-700 px-2 py-1 rounded-md text-xs">
                                <span>Design</span>
                            </span>
                            <span class="inline-flex items-center space-x-1 bg-gray-100 text-gray-700 px-2 py-1 rounded-md text-xs">
                                <span>UI</span>
                            </span>
                            <span class="inline-flex items-center space-x-1 bg-gray-100 text-gray-700 px-2 py-1 rounded-md text-xs">
                                <span>Collaboration</span>
                            </span>
                        </div>
                    </div>
                    <button onclick="window.open('https://figma.com', '_blank')" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors flex items-center justify-center space-x-2 group">
                        <span>Visit Tool</span>
                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24" class="group-hover:translate-x-1 transition-transform">
                            <path d="M19 19H5V5h7V3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2v-7h-2v7zM14 3v2h3.59l-9.83 9.83 1.41 1.41L19 6.41V10h2V3h-7z"/>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Tool Card 3 -->
            <div class="tool-card group relative bg-white rounded-xl shadow-md border border-gray-200 overflow-hidden">
                <div class="p-6">
                    <div class="mb-4">
                        <div class="inline-block px-3 py-1 rounded-full text-xs font-semibold mb-3 bg-green-100 text-green-800">
                            AI Vibe Coding Tools
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                            GitHub Copilot
                        </h3>
                        <p class="text-gray-600 text-sm leading-relaxed mb-4">
                            AI-powered code completion tool
                        </p>
                    </div>
                    <div class="mb-4">
                        <div class="flex flex-wrap gap-2">
                            <span class="inline-flex items-center space-x-1 bg-gray-100 text-gray-700 px-2 py-1 rounded-md text-xs">
                                <span>AI</span>
                            </span>
                            <span class="inline-flex items-center space-x-1 bg-gray-100 text-gray-700 px-2 py-1 rounded-md text-xs">
                                <span>Coding</span>
                            </span>
                            <span class="inline-flex items-center space-x-1 bg-gray-100 text-gray-700 px-2 py-1 rounded-md text-xs">
                                <span>Productivity</span>
                            </span>
                        </div>
                    </div>
                    <button onclick="window.open('https://github.com/features/copilot', '_blank')" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors flex items-center justify-center space-x-2 group">
                        <span>Visit Tool</span>
                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24" class="group-hover:translate-x-1 transition-transform">
                            <path d="M19 19H5V5h7V3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2v-7h-2v7zM14 3v2h3.59l-9.83 9.83 1.41 1.41L19 6.41V10h2V3h-7z"/>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Tool Card 4 -->
            <div class="tool-card group relative bg-white rounded-xl shadow-md border border-gray-200 overflow-hidden">
                <div class="absolute top-3 right-3 z-10">
                    <div class="bg-yellow-400 text-yellow-900 px-2 py-1 rounded-full text-xs font-semibold flex items-center space-x-1">
                        <svg width="12" height="12" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                        <span>Featured</span>
                    </div>
                </div>
                <div class="p-6">
                    <div class="mb-4">
                        <div class="inline-block px-3 py-1 rounded-full text-xs font-semibold mb-3 bg-yellow-100 text-yellow-800">
                            Educational Tools
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                            Notion
                        </h3>
                        <p class="text-gray-600 text-sm leading-relaxed mb-4">
                            All-in-one workspace for notes and collaboration
                        </p>
                    </div>
                    <div class="mb-4">
                        <div class="flex flex-wrap gap-2">
                            <span class="inline-flex items-center space-x-1 bg-gray-100 text-gray-700 px-2 py-1 rounded-md text-xs">
                                <span>Notes</span>
                            </span>
                            <span class="inline-flex items-center space-x-1 bg-gray-100 text-gray-700 px-2 py-1 rounded-md text-xs">
                                <span>Organization</span>
                            </span>
                            <span class="inline-flex items-center space-x-1 bg-gray-100 text-gray-700 px-2 py-1 rounded-md text-xs">
                                <span>Collaboration</span>
                            </span>
                        </div>
                    </div>
                    <button onclick="window.open('https://notion.so', '_blank')" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors flex items-center justify-center space-x-2 group">
                        <span>Visit Tool</span>
                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24" class="group-hover:translate-x-1 transition-transform">
                            <path d="M19 19H5V5h7V3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2v-7h-2v7zM14 3v2h3.59l-9.83 9.83 1.41 1.41L19 6.41V10h2V3h-7z"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
