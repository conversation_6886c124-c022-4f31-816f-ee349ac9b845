'use client'

import { useState } from 'react'
import { Plus<PERSON>ircle, Settings, Menu, X } from 'lucide-react'

interface HeaderProps {
  onAddTool?: () => void
}

export default function Header({ onAddTool }: HeaderProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  return (
    <header className="mb-12">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-4xl font-bold text-gray-800 dark:text-white mb-2">
            Directory Tools Hub
          </h1>
          <p className="text-gray-600 dark:text-gray-300 text-lg">
            Your comprehensive collection of AI tools, resources, and utilities
          </p>
        </div>
        
        <div className="hidden md:flex items-center space-x-4">
          <button
            onClick={onAddTool}
            className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
          >
            <PlusCircle size={20} />
            <span>Add Tool</span>
          </button>
          <button className="flex items-center space-x-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-200 px-4 py-2 rounded-lg transition-colors">
            <Settings size={20} />
            <span>Settings</span>
          </button>
        </div>

        <div className="md:hidden">
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="p-2 rounded-lg bg-gray-200 dark:bg-gray-700"
          >
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>
      </div>

      {/* Mobile menu */}
      {isMenuOpen && (
        <div className="md:hidden bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4 mb-6">
          <div className="space-y-2">
            <button
              onClick={onAddTool}
              className="w-full flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <PlusCircle size={20} />
              <span>Add Tool</span>
            </button>
            <button className="w-full flex items-center space-x-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-200 px-4 py-2 rounded-lg transition-colors">
              <Settings size={20} />
              <span>Settings</span>
            </button>
          </div>
        </div>
      )}

      <div className="bg-gradient-to-r from-blue-600 to-purple-600 h-1 rounded-full"></div>
    </header>
  )
}
